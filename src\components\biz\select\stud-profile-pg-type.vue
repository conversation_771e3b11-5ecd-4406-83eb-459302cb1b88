<script setup lang="ts">
import { useStudProfilePgTypeStore } from '@/store/data-tags/stud-profile-pg-type';
import { onBeforeMount } from 'vue';


const store = useStudProfilePgTypeStore();

onBeforeMount(() => {
  store.loadDataIfNeeded();
});

</script>


<template>
  <Select
    class="pima-select"
    v-bind="$attrs"
  >
    <Option
      v-for="item in store.data"
      :key="item.code"
      :value="item.code"
      :label="item.nameByLocale"
    />
  </Select>
</template>
