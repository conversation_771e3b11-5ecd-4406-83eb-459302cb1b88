<!-- eslint-disable @typescript-eslint/no-unused-vars -->
<script setup lang="ts">
import { ref, reactive, onActivated, computed } from 'vue';

import TitleBar from '@/components/common/title-bar.vue';
import CheckboxWholeTable from '@/components/biz/checkbox-whole-table.vue';
import TableScroll from '@/components/common/table-scroll.vue';
import PaginatorQt from '@/components/common/paginator-qt.vue';
import PimaImportExportCenter from '@/components/common/import-export-center.vue';
import WarningModal from '@/components/biz/modal/warning-modal.vue';
import TitleButton from './components/title-button.vue';
import SearchSimple from './components/search-simple.vue';
import QueryTable from './components/query-table.vue';


import type { EABListItemType } from '^/types/enterprise-address-book';


import { ListApi } from '@/api/enterprise-address-book/list';
import { ExportApi } from '@/api/enterprise-address-book/export';
import { namespaceT } from '@/helps/namespace-t';
import { WarningModelType } from '@/helps/models';
import { openToastError, openToastSuccess, openToastWarning } from '@/helps/toast';
import { useSider } from '@/uses/sider';
import { useQueryTable } from '@/uses/query-table';
import { useTableLoader } from '@/uses/table-loader';
import { useImportExport } from '@/uses/import-export';

import { handleKindOfBatchParams } from './helps/handle-api-data';
import { ImportApi } from '@/api/enterprise-address-book/import';
import { DownloadTemplateApi } from '@/api/enterprise-address-book/download-template';
import {
  createSearchSimpleModel,
  createDeleteModel,
  createEditModel,
  createAddModel,
  createViewModel,
} from './helps/models';
import { DeleteApi } from '@/api/enterprise-address-book/delete';


defineOptions({
  name: 'EABList',
});

const t = namespaceT('eab');
const tc = namespaceT('common');


const tableIsCheckedAll = ref(false);
const selectedRows = ref([]);

const deleteModel = reactive(createDeleteModel());
const editModel = reactive(createEditModel());
const addModel = reactive(createAddModel());
const viewModel = reactive(createViewModel());


const { getMenuName } = useSider();

const hasSelectedRows = computed(() => selectedRows.value.length > 0 || tableIsCheckedAll.value);


const loadData = useTableLoader<void, EABListItemType>(ListApi);

const qt = useQueryTable({
  load: loadData,
  simpleSearchModel: createSearchSimpleModel(),
});

const handleBatchParams = () => {
  return handleKindOfBatchParams(qt.query.value, selectedRows.value, tableIsCheckedAll.value);
};

const onExport = async () => {
  try {
    const api = new ExportApi<{ model: number }>();
    api.params = handleBatchParams();
    const data = await api.send();
    return { taskId: data.model };
  } catch (error) {
    openToastError(error.message);
    throw error;
  }
};

const onDownloadTemplate = async () => {
  try {
    const api = new DownloadTemplateApi();
    await api.send();
  } catch (error) {
    openToastError(error.message);
  }
};

const onImportFile = async (file) => {
  try {
    const api = new ImportApi();
    const formData = new FormData();

    formData.append('fileData', file.files[0]);
    api.data = formData;

    const { model: taskId } = await api.send();
    return {
      taskId,
      callback: qt.load,
    };
  } catch (error) {
    openToastError(error.message);
    throw error;
  }
};

const importExportCenter = useImportExport({
  exportData: onExport,
  importFile: onImportFile,
  downloadTemplate: onDownloadTemplate,
});


/** 重置选中内容和全选按钮 */
const resetCheck = () => {
  selectedRows.value = [];
  tableIsCheckedAll.value = false;
};


const onShowModal = (modal:WarningModelType, row:EABListItemType) => {
  Object.assign(modal, {
    visible: true,
    id: row.id,
  });
};

const onCloseModal = (modal: WarningModelType) => {
  Object.assign(modal, { visible: false });
};


// 弹窗 回调
const onSearch = () => {
  resetCheck();
  qt.search();
};


const onDelete = async () => {
  try {
    deleteModel.loading = true;
    const api = new DeleteApi({ id: deleteModel.id });
    await api.send();
    openToastSuccess(tc('hint.successfullyDeleted'));
    onCloseModal(deleteModel);
    qt.load();
  } catch (error) {
    openToastError(error.message);
  } finally {
    deleteModel.loading = false;
  }
};


onActivated(() => {
  qt.load();
});

</script>

<template>
  <TitleBar :title="getMenuName((SMC) => SMC.EAB)">
    <template #right>
      <TitleButton
        :exporting="importExportCenter.exporting"
        :importing="importExportCenter.importing"
        :downloading="importExportCenter.downloadingTemplate"
        @on-download-template="importExportCenter.downloadTemplate"
        @on-import="importExportCenter.importFile"
        @on-export="importExportCenter.exportData"
        @on-add="addModel.visible = true"
      />
    </template>
  </TitleBar>

  <SearchSimple
    v-model="qt.simpleSearchModel"
    @on-search="onSearch"
  />

  <TableScroll>
    <CheckboxWholeTable
      v-if="qt.table.total > 0"
      v-model="tableIsCheckedAll"
      :total="qt.table.total"
    />

    <QueryTable
      v-model:selected="selectedRows"
      :is-checked-all="tableIsCheckedAll"
      :data="qt.table.data"
      :loading="qt.table.loading"
      @on-view="(row:EABListItemType)=>onShowModal(viewModel,row)"
      @on-edit="(row:EABListItemType)=>onShowModal(editModel,row)"
      @on-delete="(row:EABListItemType)=>onShowModal(deleteModel,row)"
    />

    <template #paginator>
      <PaginatorQt
        :query-table="qt"
      />
    </template>
  </TableScroll>

  <WarningModal
    v-model="deleteModel.visible"
    v-bind="deleteModel"
    @on-confirm="onDelete"
    @on-cancel="onCloseModal(deleteModel)"
  />


  <PimaImportExportCenter />
</template>
