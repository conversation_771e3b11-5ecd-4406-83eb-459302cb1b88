<script setup lang="ts">
import { onBeforeUnmount, onMounted, ref } from 'vue';
import { storeToRefs } from 'pinia';
import { useCurInfoStore } from '@/store/cur-info';

defineOptions({
  name: 'WaterMark',
});

const watermarkCanvas = ref<HTMLCanvasElement>();

const userInfoStore = useCurInfoStore();
const { data: currentUserInfo } = storeToRefs(userInfoStore);

const drawWatermark = () => {
  const canvas = watermarkCanvas.value;
  if (!canvas) return;

  const context = canvas.getContext('2d');
  if (!context) return;

  const parent = watermarkCanvas.value.parentElement;
  canvas.width = parent ? parent.clientWidth : window.innerWidth;
  canvas.height = parent ? parent.clientHeight : window.innerHeight;

  const angle = -15 * (Math.PI / 180);
  const fontSize = 20;
  context.font = `${fontSize}px Arial`;
  context.fillStyle = 'rgba(180, 180, 180, 0.2)';

  const fillText = `${currentUserInfo.value.name} ${currentUserInfo.value.userNo}`;
  const textWidth = context.measureText(fillText).width;
  for (let x = 0; x < canvas.width; x += textWidth * 2.2) {
    for (let y = 0; y < canvas.height; y += textWidth * 1.4) {
      context.save();
      context.translate(x, y);
      context.rotate(angle);
      context.fillText(fillText, 0, textWidth * 0.5);
      context.restore();
    }
  }
};

onMounted(async () => {
  await userInfoStore.loadDataIfNeeded();
  drawWatermark();

  window.addEventListener('resize', drawWatermark);
});

onBeforeUnmount(() => {
  window.removeEventListener('resize', drawWatermark);
});
</script>

<template>
  <canvas ref="watermarkCanvas" />
</template>


<style lang="less" scoped>
canvas {
  position: absolute;
  top: 0;
  left: 0;
  z-index: 99999;
  pointer-events: none;
}
</style>
