{"editor.codeActionsOnSave": {"source.fixAll.eslint": "explicit"}, "eslint.validate": ["javascript", "javascriptreact", "typescript", "typescriptreact", "vue"], "eslint.format.enable": true, "editor.formatOnSave": true, "editor.defaultFormatter": "dbaeumer.vscode-eslint", "[vue]": {"editor.defaultFormatter": "dbaeumer.vscode-eslint"}, "[typescript]": {"editor.defaultFormatter": "dbaeumer.vscode-eslint"}, "[javascript]": {"editor.defaultFormatter": "dbaeumer.vscode-eslint"}, "cSpell.words": ["<PERSON><PERSON><PERSON><PERSON>"]}