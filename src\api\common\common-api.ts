import { LOCAL_ENTERPRISE_ADDRESS_BOOK_API_BASE_URL, API_SALT } from '@/config/api';
import { LOCALE_COOKIE_KEY } from '@/config/cookie';
import { type RequestMethod, type RequestParams, type RequestData, BaseRequestApi } from '../base/base-request-api';

export { type RequestMethod, type RequestParams, type RequestData };


export class CommonApi<T> extends BaseRequestApi<T> {
  constructor(args?) {
    super({
      /** Tips :基础接口内容取自用户信息 */
      baseURL: LOCAL_ENTERPRISE_ADDRESS_BOOK_API_BASE_URL,
      salt: API_SALT,
      localeCookieKey: LOCALE_COOKIE_KEY,
      ...args,
    });
  }
}
