import { CommonApi } from '@/api/common/common-api';
import { BaseError } from '@/errors/base-error';
import { namespaceT } from '@/helps/namespace-t';


enum ErrorCodes {
  EXPORT_DATA_LIMIT = 'EXPORT_DATA_LIMIT',
  CURRENT_ACCOUNT_IS_NOT_ENTERPRISE_ACCOUNT = 'CURRENT_ACCOUNT_IS_NOT_ENTERPRISE_ACCOUNT',
}

export class ExportApi<T> extends CommonApi<T> {
  url() {
    return '/enterprise/job-posts/export';
  }

  async send(): Promise<T> {
    try {
      const res = await super.send();
      return res;
    } catch (error) {
      const t = namespaceT('apiErrors.jobPost.export');

      switch (error.code) {
        case ErrorCodes.EXPORT_DATA_LIMIT:
        case ErrorCodes.CURRENT_ACCOUNT_IS_NOT_ENTERPRISE_ACCOUNT:
          throw new BaseError(t(error.code));
        default:
          throw error;
      }
    }
  }
}
