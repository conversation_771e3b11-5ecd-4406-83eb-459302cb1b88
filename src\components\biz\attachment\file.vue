<script setup lang="ts">
interface Props {
  fileName: string
}

defineProps<Props>();
</script>

<template>
  <div class="file">
    <Icon
      type="ios-attach"
      :size="24"
      class="icon-attach"
    />

    <a
      v-bind="$attrs"
      target="_blank"
    >
      {{ fileName }}
    </a>
  </div>
</template>

<style lang="less" scoped>
.file {
  display: flex;
  gap: 8px;
  align-items: center;
  word-break: break-word;

  .icon-attach {
    transform: rotate(40deg);
  }

  a {
    flex: 1;
    overflow: hidden;
    color: #111;
    font-weight: 400;
    font-size: 14px;
    white-space: nowrap;
    text-overflow: ellipsis;

    &:hover {
      color: var(--primary-color);
    }
  }
}
</style>
