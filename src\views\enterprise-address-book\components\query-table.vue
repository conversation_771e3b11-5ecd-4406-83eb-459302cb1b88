<script lang="ts" setup>
import { getCurrentInstance } from 'vue';

import type { EABListItemType } from '^/types/enterprise-address-book';

import TableSelectable from '@/components/common/table-selectable';
import TableActionWrapper from '@/components/common/table-action-wrapper.vue';
import TableAction from '@/components/common/table-action';
import DefaultText from '@/components/common/default-text.vue';

import { isY } from '@/helps/y-o-n';
import { namespaceT } from '@/helps/namespace-t';

import { createColumns } from '../helps/columns';
import { formateToDate } from '@/helps/formate-to-date-type';


defineProps<{
  isCheckedAll: boolean;
  data: EABListItemType[];
  loading: boolean;
}>();

const emit = defineEmits<{
  'on-view': [row:EABListItemType]
  'on-edit': [row:EABListItemType]
  'on-delete': [row:EABListItemType]
}>();

const selectedIds = defineModel<EABListItemType[]>('selected');

const vm = getCurrentInstance();
const t = namespaceT('eab');
const columns = createColumns();


const can = (type: string) => {
  return vm.proxy.$can((P) => P.EAB[type]);
};


const actions = [
  {
    label: t('action.view'),
    triggerEvent: (row:EABListItemType) => emit('on-view', row),
    can: (row) => isY(row.deletableInd),
  },
  {
    label: t('action.edit'),
    triggerEvent: (row:EABListItemType) => emit('on-edit', row),
    can: (row) => isY(row.editableInd) && can('Edit'),
  },
  {
    label: t('action.delete'),
    triggerEvent: (row:EABListItemType) => emit('on-delete', row),
    can: (row) => isY(row.editableInd) && can('Remove'),
  },
];


const onUpdateSelected = (list:EABListItemType[]) => {
  selectedIds.value = list;
};


</script>


<template>
  <TableSelectable
    :is-checked-all="isCheckedAll"
    :columns="columns"
    :data="data"
    :loading="loading"
    :selected="selectedIds"
    @update:selected="onUpdateSelected"
  >
    <!-- 联系人信息 -->
    <template #contactInfo="{ row }">
      {{ row.contactInfo }}
    </template>

    <!-- 地址 -->
    <template #addr="{ row }">
      {{ row.addr }}
    </template>

    <!-- 来源渠道 -->
    <template #sourceChannel="{ row }">
      {{ row.sourceChannel }}
    </template>

    <!-- 过往合作经历 -->
    <template #cooperationHistory="{ row }">
      {{ row.cooperationHistory }}
    </template>

    <!-- 备注 -->
    <template #remark="{ row }">
      {{ row.remark }}
    </template>


    <!-- 操作人/时间 -->
    <template #operatorAndTime="{ row }">
      <DefaultText :text="row.updName" />
      <br>
      <DefaultText :text="formateToDate(row.updTime,'dateTime')" />
    </template>


    <!-- 操作 -->
    <template #operation="{ row }">
      <TableActionWrapper>
        <TableAction
          :row-data="row"
          :limit="3"
          :actions="actions"
        />
      </TableActionWrapper>
    </template>
  </TableSelectable>
</template>
