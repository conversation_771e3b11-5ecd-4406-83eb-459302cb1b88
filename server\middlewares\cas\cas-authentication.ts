/* eslint-disable */
// 该文件为 CASAuthentication 部分方法重写，原则是不修改旧有源码，只处理有变动部分，故不做eslint校验
module.exports = function createCasAuthentication() {
  const url = require('url');
  const CASAuthentication = require('cas-authentication');


  /**
   * The CAS authentication types.
   * @enum {number}
   */
  var AUTH_TYPE = {
    BOUNCE          : 0,
    BOUNCE_REDIRECT : 1,
    BLOCK           : 2
  };
  
  // 重写_login方法，去除跳转时的renew参数
  CASAuthentication.prototype._login = function(req, res) {
    // Save the return URL in the session. If an explicit return URL is set as a
    // query parameter, use that. Otherwise, just use the URL from the request.
    req.session.cas_return_to = req.query.returnTo || url.parse(req.url).path;
  
    // Set up the query parameters.
    const query = {
      service: this.service_url + url.parse(req.url).pathname,
    };
  
    // Redirect to the CAS login.
    res.redirect(this.cas_url + url.format({
      pathname: '/login',
      query: {
        ...query,
        ...res.locals.casLoginQuery,
      },
    }));
  };
  
  /**
   * Handle a request with CAS authentication.
   */
  CASAuthentication.prototype._handle = function(req, res, next, authType) {
  
    // If the session has been validated with CAS, no action is required.
    if (req.session[ this.session_name ]) {
        // If this is a bounce redirect, redirect the authenticated user.
        if (authType === AUTH_TYPE.BOUNCE_REDIRECT) {
            res.redirect(req.session.cas_return_to);
        }
        // Otherwise, allow them through to their request.
        else {
            next();
        }
    }
    // If dev mode is active, set the CAS user to the specified dev user.
    else if (this.is_dev_mode) {
        req.session[ this.session_name ] = this.dev_mode_user;
        req.session[ this.session_info ] = this.dev_mode_info;
        next();
    }
    // If the authentication type is BLOCK, simply send a 401 response.
    else if (authType === AUTH_TYPE.BLOCK) {
        console.log(new Date().toISOString(), 'CAS authentication is blocked', req.url, req.cookies, req.session);
        res.sendStatus(401);
    }
    // If there is a CAS ticket in the query string, validate it with the CAS server.
    else if (req.query && req.query.ticket) {
        // 修复由于没有session里cas_return_to的跳转问题
        if (!req.session.cas_return_to) {
          req.session.cas_return_to = this.service_url + url.parse(req.url).pathname;
        }
  
        this._handleTicket(req, res, next);
    }
    // Otherwise, redirect the user to the CAS login.
    else {
        // 已变更为应用错误处理控制是否需要登录
        // this._login(req, res, next);
        next();
    }
  };
  
  // 重写logout方法，支持跳转时增加service参数
  CASAuthentication.prototype.logout = function(req, res, next) {
    // Destroy the entire session if the option is set.
    if (this.destroy_session) {
      req.session.destroy(function(err) {
        if (err) {
          console.log(err);
        }
      });
    }
    // Otherwise, just destroy the CAS session variables.
    else {
      delete req.session[this.session_name];
      if (this.session_info) {
        delete req.session[this.session_info];
      }
    }
  
    // Redirect the client to the CAS logout.
    if (req.query.service) {
      res.redirect(`${this.cas_url}/logout?service=${encodeURIComponent(req.query.service)}`);
    } else {
      res.redirect(`${this.cas_url}/logout`);
    }
  };
  
  function inheritPrototype(subType, superType){
    const prototype = Object.create(superType.prototype);
    prototype.constructor = subType;
    subType.prototype = prototype;
  }
  
  function SubCasAuthentication(options) {
    CASAuthentication.call(this, options);
    const parsed_cas_url = url.parse(this.cas_url);
  
    // 支持端口号配置
    this.cas_port = parsed_cas_url.port || (parsed_cas_url.protocol === 'http:' ? 80 : 443);
  }
  
  inheritPrototype(SubCasAuthentication, CASAuthentication);


  return SubCasAuthentication;
};
