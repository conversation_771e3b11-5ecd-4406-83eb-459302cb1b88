<script lang="ts" setup>
import { toRef } from 'vue';

import type { AttachmentVO } from '^/types/attachment';
import { pickFiles } from '@/utils/pick-files';
import { namespaceT } from '@/helps/namespace-t';
import { openToastError } from '@/helps/toast';


const props = withDefaults(defineProps<{
  modelValue: AttachmentVO[];
  loading?: boolean;
  uploading?: boolean;
  accept?: string;
  access?: string[];
  limitSize?: number;
}>(), {
  loading: false,
  uploading: false,
  accept: '',
  access: () => [],
  limitSize: 0,
});

interface EmitType {
  (e: 'on-upload', file: File): void
  (e: 'on-remove', file: AttachmentVO): void
}
const emit = defineEmits<EmitType>();

const tm = namespaceT('common');
const model = toRef(props, 'modelValue');

function validType(file: File) {
  if (props.access.length === 0) {
    return true;
  }

  const suffix = file.name.substring(file.name.lastIndexOf('.'));

  if (props.access.some((o) => o.trim().toLowerCase() === suffix.trim().toLowerCase())) {
    return true;
  }
  openToastError(tm('error.fileTypeError', { accept: props.access.join('、') }));
  return false;
}

function validSize(file: File) {
  if (!props.limitSize) {
    return true;
  }
  const largestSize = props.limitSize * 1024 * 1024;
  if (file.size > largestSize) {
    openToastError(tm('error.fileSizeError', { size: props.limitSize }));
    return false;
  }
  return true;
}

function onPick({ files }) {
  const file = files[0];
  if (validType(file) && validSize(file)) {
    emit('on-upload', file);
  }
}

function onUpload() {
  pickFiles({
    accept: props.accept,
    onPick,
  });
}

function onRemove(file) {
  emit('on-remove', file);
}

</script>


<template>
  <div class="upload">
    <div class="upload-box">
      <Button
        type="primary"
        class="pima-btn"
        :loading="uploading"
        @click="onUpload()"
      >
        {{ tm('action.upload') }}
      </Button>
      <div class="file-desertions">
        <slot name="file-desertions" />
      </div>
    </div>
    <div class="file-list">
      <div
        v-for="file in model"
        :key="`file-${file.id}`"
        class="file-item"
      >
        <template v-if="file.id">
          <Icon
            type="ios-attach"
            :size="24"
            class="icon-attach"
          />
          <a
            :href="file.filePath.normal"
            target="_blank"
          >
            {{ file.origFileName }}
          </a>
          <Icon
            type="ios-close"
            :size="24"
            class="icon-close"
            @click="onRemove(file)"
          />
        </template>
      </div>
    </div>
  </div>
</template>


<style lang="less" scoped>
.upload {
  width: 100%;
}

.upload-box {
  display: flex;
  flex-direction: column;
  gap: 8px;
  align-items: baseline;
}

.file-list {
  margin-top: 12px;
}

.file-item {
  display: flex;
  align-items: center;
  margin-bottom: 8px;

  .icon-attach {
    margin-right: 8px;
    transform: rotate(40deg);
  }

  .icon-close {
    margin-left: 12px;
    cursor: pointer;
  }
}
</style>
