export default {
  common: {
    NO_EXISTS: '数据不存在',
  },

  studentInfo: {
    detail: {
      NO_EXISTS: '@:apiErrors.common.NO_EXISTS',
    },

    edit: {
      NO_EXISTS: '@:apiErrors.common.NO_EXISTS',
    },

    removeBatch: {
      NO_EXISTS: '请选择需要移出班级的学生',
    },
  },

  classMgt: {
    setGraduated: {
      INVALID_FIELD: '请选择需要处理的班级',
      STATUS_ABNORMAL: '只能设置班级情况为学生在读的数据为毕业班',
    },

    setGraduatedBatch: {
      INVALID_FIELD: '@:apiErrors.classMgt.setGraduated.INVALID_FIELD',
      STATUS_ABNORMAL: '@:apiErrors.classMgt.setGraduated.STATUS_ABNORMAL',
    },

    delete: {
      NO_EXISTS: '@:apiErrors.common.NO_EXISTS',
      STATUS_ABNORMAL: '学生已毕业，不可删除',
      CLS_EXIST_STUDENT: '该班级存在学生信息，请先前往“学生资料”菜单，移除班级中的学生，再进行删除操作！',
    },

    edit: {
      NO_EXISTS: '@:apiErrors.common.NO_EXISTS',
    },

    detail: {
      NO_EXISTS: '@:apiErrors.common.NO_EXISTS',

    },
  },

  personnel: {
    edit: {
      NO_EXISTS: '@:apiErrors.common.NO_EXISTS',
    },

    detail: {
      NO_EXISTS: '@:apiErrors.common.NO_EXISTS',
    },

    updateMobile: {
      NO_EXISTS: '找不到学生信息',
      BUSY: '验证码有误或已过期',
      INVALID_FIELD: '手机号格式有误',
      ACCESS_DENIED: '已绑定手机号有误',
      ALREADY_EXISTS: '新手机号已存在',
    },

    updateEmail: {
      BUSY: '验证码有误或已过期',
      NO_EXISTS: '找不到学生信息',
      ALREADY_EXISTS: '邮箱已存在',
    },

    sendSms: {
      BUSY: '验证码发送失败',
    },
  },

  dfs: {
    ATTACHMENT_DISALLOWED_EXTENSIONS_ERROR: '附件不允许该扩展名',
    ATTACHMENT_NAME_LENGTH: '附件名长度错误',
  },
};
