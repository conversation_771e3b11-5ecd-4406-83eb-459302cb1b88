import type { ColumnsType } from '^/types/columns';

import { namespaceT } from '@/helps/namespace-t';

export const createColumns = (): ColumnsType[] => {
  const t = namespaceT('eab.columns');
  const tc = namespaceT('common.table');

  return [
    // 企业名称
    {
      title: t('entName'),
      key: 'entName',
      minWidth: 110,
    },

    // 联系人信息
    {
      title: t('contactInfo'),
      slot: 'contactInfo',
      minWidth: 200,
    },

    // 地址
    {
      title: t('addr'),
      slot: 'addr',
      minWidth: 100,
    },

    // 来源渠道
    {
      title: t('sourceChannel'),
      slot: 'sourceChannel',
      minWidth: 100,
    },

    // 过往合作经历
    {
      title: t('cooperationHistory'),
      slot: 'cooperationHistory',
      minWidth: 120,
    },

    // 备注
    {
      title: t('remark'),
      slot: 'remark',
      minWidth: 100,
    },

    // 操作人/时间
    {
      title: t('operatorAndTime'),
      slot: 'operatorAndTime',
      minWidth: 100,
    },

    // 操作
    {
      title: tc('operation'),
      slot: 'operation',
      width: 180,
    },
  ];
};
