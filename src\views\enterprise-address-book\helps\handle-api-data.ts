import _ from 'lodash';

import type { EABListItemType, SearchSimpleModelType } from '^/types/enterprise-address-book';

import { createSearchSimpleModel } from './models';


/** 处理批量类型接口  如 导出  */
export const handleKindOfBatchParams = (
  params:SearchSimpleModelType,
  selectedRows:EABListItemType[],
  isSelectAll:boolean,
) => {
  const cloneParams = _.cloneDeep(params);

  //  全选则不传ids
  cloneParams.ids = !isSelectAll ? selectedRows.map((item) => item.id) : null;

  return _.pick(cloneParams, Object.keys(createSearchSimpleModel()));
};
