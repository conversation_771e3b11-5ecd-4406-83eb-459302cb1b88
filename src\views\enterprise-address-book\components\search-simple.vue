<script setup lang="ts">
import type { SearchSimpleModelType } from '^/types/enterprise-address-book';

import WrapperSearchSimple from '@/components/common/wrapper-search-simple.vue';
import PairLabelItem from '@/components/common/pair-label-item.vue';
import InputSearch from '@/components/common/input-search.vue';

import { namespaceT } from '@/helps/namespace-t';

const emit = defineEmits<{
  'on-search': [],
}>();

const t = namespaceT('eab');
const model = defineModel<SearchSimpleModelType>();

const emitSearch = () => {
  emit('on-search');
};
</script>


<template>
  <WrapperSearchSimple>
    <PairLabelItem no-colon>
      <InputSearch
        v-model.trim="model.keyword"
        :placeholder="t('placeholder.keyword')"
        class="w-300"
        clearable
        @on-clear="emitSearch"
        @on-search="emitSearch"
      />
    </PairLabelItem>
  </WrapperSearchSimple>
</template>
