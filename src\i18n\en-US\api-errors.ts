/* eslint-disable max-len */
export default {
  common: {
    NO_EXISTS: 'Data does not exist',
  },

  studentInfo: {
    detail: {
      NO_EXISTS: '@:apiErrors.common.NO_EXISTS',
    },

    edit: {
      NO_EXISTS: '@:apiErrors.common.NO_EXISTS',
    },

    removeBatch: {
      NO_EXISTS: 'Please select students to remove from class',
    },
  },

  classMgt: {
    setGraduated: {
      INVALID_FIELD: 'Please select classes to process',
      STATUS_ABNORMAL: 'Only classes with students currently studying can be set as graduated',
    },

    setGraduatedBatch: {
      INVALID_FIELD: '@:apiErrors.classMgt.setGraduated.INVALID_FIELD',
      STATUS_ABNORMAL: '@:apiErrors.classMgt.setGraduated.STATUS_ABNORMAL',
    },

    delete: {
      NO_EXISTS: '@:apiErrors.common.NO_EXISTS',
      STATUS_ABNORMAL: 'Students have graduated, cannot delete',
      CLS_EXIST_STUDENT: 'This class contains student information. Please go to "Student Information" menu first, remove students from the class, then proceed with deletion!',
    },

    edit: {
      NO_EXISTS: '@:apiErrors.common.NO_EXISTS',
    },

    detail: {
      NO_EXISTS: '@:apiErrors.common.NO_EXISTS',

    },
  },

  personnel: {
    edit: {
      NO_EXISTS: '@:apiErrors.common.NO_EXISTS',
    },

    detail: {
      NO_EXISTS: '@:apiErrors.common.NO_EXISTS',
    },

    updateMobile: {
      NO_EXISTS: 'Student information not found',
      BUSY: 'Verification code is incorrect or expired',
      INVALID_FIELD: 'Mobile phone format is incorrect',
      ACCESS_DENIED: 'Bound mobile phone number is incorrect',
      ALREADY_EXISTS: 'New mobile phone number already exists',
    },

    updateEmail: {
      BUSY: 'Verification code is incorrect or expired',
      NO_EXISTS: 'Student information not found',
      ALREADY_EXISTS: 'Email already exists',
    },

    sendSms: {
      BUSY: 'Failed to send verification code',
    },
  },

  dfs: {
    ATTACHMENT_DISALLOWED_EXTENSIONS_ERROR: 'Attachment disallowed extension',
    ATTACHMENT_NAME_LENGTH: 'Attachment name length error',
  },
};
