<script setup lang="ts">
import { namespaceT } from '@/helps/namespace-t';

interface Props {
  exporting: boolean;
  importing: boolean;
  downloading: boolean;
}

defineProps<Props>();

const emit = defineEmits<{
  'on-download-template':[],
  'on-import':[],
  'on-add':[],
  'on-export':[],
}>();


const t = namespaceT('eab');

</script>


<template>
  <Button
    v-if="$can((P)=>P.EAB.Import)"
    class="pima-btn mr-15"
    type="text"
    :loading="downloading"
    @click="emit('on-download-template')"
  >
    {{ t('action.downloadTemplate') }}
  </Button>

  <Button
    v-if="$can((P)=>P.EAB.Import)"
    class="pima-btn mr-15"
    type="primary"
    :loading="importing"
    @click="emit('on-import')"
  >
    {{ t('action.import') }}
  </Button>

  <Button
    v-if="$can((P)=>P.EAB.Add)"
    class="pima-btn mr-15"
    type="primary"
    @click="emit('on-add')"
  >
    {{ t('action.add') }}
  </Button>

  <Button
    v-if="$can((P)=>P.EAB.Export)"
    class="pima-btn"
    type="primary"
    :loading="exporting"
    @click="emit('on-export')"
  >
    {{ t('action.export') }}
  </Button>
</template>
