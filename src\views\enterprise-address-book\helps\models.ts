import type { SearchSimpleModelType } from '^/types/enterprise-address-book';

import { namespaceT } from '@/helps/namespace-t';
import { createWarningModel } from '@/helps/models';

export const createSearchSimpleModel = (): SearchSimpleModelType => {
  return {
    keyword: undefined,
  };
};


export const createDeleteModel = () => {
  const t = namespaceT('eab.modal.delete');

  return createWarningModel({
    title: t('title'),
    content: t('content'),
  });
};


export const createEditModel = () => {
  const t = namespaceT('eab.title');

  return createWarningModel({
    title: t('edit'),
  });
};


export const createAddModel = () => {
  const t = namespaceT('eab.title');

  return createWarningModel({
    title: t('add'),
  });
};

export const createViewModel = () => {
  const t = namespaceT('eab.title');

  return createWarningModel({
    title: t('view'),
  });
};
