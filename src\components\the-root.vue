<script lang='ts' setup>
import { getCurrentInstance, ref, provide, onBeforeMount, nextTick, computed } from 'vue';
import { useRoute } from 'vue-router';
import type { Composer } from 'vue-i18n';
import URI from 'urijs';

// eslint-disable-next-line import/order
import TheTheme from './the-theme.vue';
// eslint-disable-next-line import/order
import TheLayout from './the-layout.vue';
// eslint-disable-next-line import/order
import TheHeader from './the-header.vue';
// eslint-disable-next-line import/order
import TheSider from './the-sider.vue';

// eslint-disable-next-line import/order
import ModalAccountError from './biz/modal-account-error.vue';

import ForbiddenPage from '@/views/forbidden.vue';
import { MENU_NAME_INJECTION_KEY } from '@/config/sider-menu';
import { KeepAliveInclude, KeepAliveExclude } from '@/config/keep-alive';
import { PUBLIC_PATH } from '@/config/public-path';
import { useSider } from '@/uses/sider';
import emitter from '@/utils/event-bus';
import { getLocale } from '@/helps/locale';
import { i18n } from '@/i18n';


const vm = getCurrentInstance();
const locale = getLocale();
const sider = useSider();
const showView = ref(true);
const accountErrorCode = ref('');
const isShowModalAccountError = ref(false);
const route = useRoute();
const loaded = ref(false);


provide(MENU_NAME_INJECTION_KEY, sider.getMenuName);


function handleLogout() {
  const uri = new URI(PUBLIC_PATH);
  uri.segment('logout');
  uri.search({
    service: window.location.href,
    appId: process.env.APP_ID,
    locale: (i18n.global as unknown as Composer).locale.value,
  });
  window.location.replace(uri.toString());
}

function bindEvents() {
  // 公共头部聚合了初始数据，在这里监听数据获取情况
  emitter.on('userDataFetched', ({ error, operations }) => {
    if (error) {
      return;
    }

    vm.proxy.$setAuths(operations);
    loaded.value = true;
  });


  const reload = () => {
    window.location.reload();
  };

  // 如果接口出现用户未认证错误，则刷新界面
  emitter.on('accountUnauthorizedError', () => {
    reload();
  });

  // 如果刷新Token失败，则刷新界面
  emitter.on('accessTokenInvalidError', () => {
    reload();
  });

  // 如果刷新Token失败，则刷新界面
  emitter.on('refreshTokenInvalidError', () => {
    handleLogout();
  });

  emitter.on('accountDisabledError', (error) => {
    accountErrorCode.value = error.code;
    isShowModalAccountError.value = true;
  });
}

const hasRouterAuth = computed(() => {
  const authCodes = route?.meta?.baseAuthCodes as Array<string> || [];

  if (authCodes.length) {
    const routerAuthArr = authCodes.map((item) => {
      return vm.proxy.$can(() => item);
    });

    return routerAuthArr.some((i) => i);
  }
  return true;
});

function rebuildView() {
  showView.value = false;
  nextTick(() => {
    showView.value = true;
  });
}

onBeforeMount(() => {
  bindEvents();
});

</script>


<template>
  <TheTheme :locale="locale">
    <TheLayout :has-sider="sider.isShow.value && sider.menu.value.length > 1">
      <template #header>
        <TheHeader @on-logout="handleLogout" />
      </template>

      <template #sider>
        <TheSider
          v-show="sider.isShow.value && sider.menu.value.length > 1"
          @rebuild="rebuildView()"
        />
      </template>

      <template #main>
        <RouterView
          v-if="$hasAuths() && hasRouterAuth && showView"
          v-slot="{ Component, route }"
        >
          <KeepAlive
            :include="KeepAliveInclude"
            :exclude="KeepAliveExclude"
          >
            <component
              :is="Component"
              :key="route.fullPath"
            />
          </KeepAlive>
        </RouterView>
        <ForbiddenPage v-else-if="loaded" />
      </template>
    </TheLayout>

    <!-- 账户出错Modal窗 -->
    <ModalAccountError
      v-model="isShowModalAccountError"
      :account-error-code="accountErrorCode"
      @on-logout="handleLogout"
    />
  </TheTheme>
</template>
