import { CommonApi, RequestMethod } from '@/api/common/common-api';
import { BaseError } from '@/errors/base-error';
import { namespaceT } from '@/helps/namespace-t';


enum ErrorCodes {
  NO_EXISTS = 'NO_EXISTS',
  JOB_STATUS_ERROR = 'JOB_STATUS_ERROR',
}

export class DeleteApi<T> extends CommonApi<T> {
  id:number;

  constructor({ id }) {
    super();
    this.id = id;
  }

  url() {
    return `/enterprise/job-posts/${this.id}/remove`;
  }

  method(): RequestMethod {
    return 'POST';
  }

  async send(): Promise<T> {
    try {
      const res = await super.send();
      return res;
    } catch (error) {
      const t = namespaceT('apiErrors.jobPost.delete');

      switch (error.code) {
        case ErrorCodes.NO_EXISTS:
        case ErrorCodes.JOB_STATUS_ERROR:
          throw new BaseError(t(error.code));
        default:
          throw error;
      }
    }
  }
}
