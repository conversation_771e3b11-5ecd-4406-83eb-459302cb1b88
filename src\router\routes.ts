import type { RouteRecordRaw } from 'vue-router';
import { PUBLIC_PATH } from '@/config/public-path';
import { RouterName as RN } from '@/config/router';
import { SiderMenuCodes as SMC } from '@/config/sider-menu';
import { Auth } from '@/config/auth';

import TheRoot from '@/components/the-root.vue';


export const routes = Object.freeze<RouteRecordRaw[]>([
  {
    path: PUBLIC_PATH,
    name: RN.Root,
    component: TheRoot,
    children: [

      /** 企业通讯录 */
      {
        path: 'enterprise-address-book',
        name: RN.EAB,
        component: () => import('@/views/enterprise-address-book'),
        meta: {
          requiresAuth: true, // 不设置或设置为true需要认证
          activeCodes: [SMC.EAB],
          baseAuthCodes: [Auth.EAB.View],
        },
      },


      {
        path: 'forbidden',
        name: RN.Forbidden,
        component: () => import('@/views/forbidden.vue'),
      },
    ],
  },
]);
